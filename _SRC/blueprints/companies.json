{"blueprint": {"chart_of_accounts": {"immutable": false, "name": "Chart of Accounts", "objectType": "chart_of_accounts", "selectDisplay": "[name]", "type": "objectIds"}, "child_ids": {"immutable": true, "name": "Children", "type": "string"}, "color": {"immutable": false, "name": "Color", "type": "text"}, "contact_info": {"immutable": true, "name": "Contact Info", "objectType": "contact_info", "selectDisplay": "[info]", "type": "objectIds"}, "created_by": {"immutable": true, "name": "Created By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "data_source": {"immutable": true, "name": "Data Source", "type": "int"}, "data_source_id": {"immutable": true, "name": "Data Source Id", "type": "int"}, "data_source_hash": {"immutable": true, "name": "Data Source Hash", "type": "string"}, "date_created": {"immutable": true, "name": "Date Created", "type": "date"}, "default_product": {"immutable": false, "name": "Default Product", "objectType": "inventory_categories", "selectDisplay": "[name]", "type": "objectId"}, "id": {"immutable": true, "name": "id", "type": "int"}, "is_vendor": {"immutable": false, "name": "Is vendor?", "type": "int"}, "is_active": {"immutable": false, "name": "Is active?", "type": "int"}, "last_updated": {"immutable": true, "name": "Last Updated", "type": "date"}, "last_updated_by": {"immutable": true, "name": "Last Updated By", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "manager": {"immutable": false, "name": "Manager", "objectType": "staff", "selectDisplay": "[fname] [lname]", "type": "objectId"}, "markup_percent": {"immutable": false, "name": "<PERSON><PERSON><PERSON>", "type": "float"}, "name": {"immutable": false, "name": "Name", "type": "string"}, "object_uid": {"immutable": true, "name": "Object Id", "type": "int"}, "parent_id": {"immutable": true, "name": "Parent", "type": "int"}, "products": {"immutable": false, "name": "Products", "objectType": "inventory_categories", "selectDisplay": "[name]", "type": "objectIds"}, "profile_image": {"immutable": false, "name": "Profile Image", "objectType": "file_meta_data", "selectDisplay": "[name]", "type": "objectId"}, "tax_exempt": {"immutable": false, "name": "Tax Exempt", "type": "boolean"}, "type": {"immutable": false, "name": "Type", "objectType": "company_categories", "selectDisplay": "[name]", "type": "objectId"}, "value": {"immutable": true, "name": "Value", "type": "usd"}, "date_booked": {"immutable": false, "name": "Date Booked", "type": "date"}}, "blueprint_name": "companies", "blueprint_type": "object", "date_created": "2017-03-04 17:31:51.442101", "id": 266, "instance": "<PERSON><PERSON><PERSON><PERSON>"}